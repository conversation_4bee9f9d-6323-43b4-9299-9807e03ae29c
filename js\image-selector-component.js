// image-selector-component.js
const ImageSelectorComponent = {
    props: {
        modelValue: {
            type: String,
            default: ''
        },
        showAddButton: {
            type: Boolean,
            default: true
        },
        minScale: {
            type: Number,
            default: 0.5
        },
        maxScale: {
            type: Number,
            default: 3
        }
    },
    emits: ['update:modelValue', 'image-change'],
    setup(props, { emit }) {
        const { ref, computed } = Vue;
        
        const fileInput = ref(null);
        const select_img = ref(props.modelValue);
        
        // 图片变换相关状态
        const imageTransform = ref({
            width: 67, // 使用vw单位的数值
            translateX: 0,
            translateY: 0
        });
        
        // 触摸相关状态
        const touchState = ref({
            isTouch: false,
            startDistance: 0,
            startWidth: 67,
            startX: 0,
            startY: 0,
            startTranslateX: 0,
            startTranslateY: 0,
            touches: []
        });
        
        // 鼠标事件处理（桌面端支持）
        const mouseState = ref({
            isDragging: false,
            startX: 0,
            startY: 0,
            startTranslateX: 0,
            startTranslateY: 0
        });
        
        // 计算图片样式
        const imageStyle = computed(() => {
            return {
                width: `${imageTransform.value.width}vw`,
                position: 'absolute',
                pointerEvents: 'auto',
                touchAction: 'none',
                userSelect: 'none',
                WebkitUserSelect: 'none',
                MozUserSelect: 'none',
                msUserSelect: 'none',
                cursor: 'move',
                transform: `translate(${imageTransform.value.translateX}px, ${imageTransform.value.translateY}px)`,
                transformOrigin: 'center center',
                transition: touchState.value.isTouch ? 'none' : 'transform 0.3s ease, width 0.3s ease'
            };
        });
        const addStle = computed(() => {
            return {
                width: '12vw',
                height: '12vw',
                background: `url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFsAAABbBAMAAADuGOSqAAAAD1BMVEUAAADu7u7u7u7u7u7u7u4syuuIAAAABXRSTlMAjDgxVgvJSGkAAABGSURBVFjD7dOhDQAgDETRJixAAwMQwv4zIsCdqjvxv+2TvdBavkZocDgcDofb8S2dz6eeVmSlDoeb8dq/e20VDofD4fAyv2XWYbSlyh17AAAAAElFTkSuQmCC) no-repeat center center/100% 100%`,
                position: 'absolute',
                top: '42%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                pointerEvents: 'auto',
                cursor: 'pointer',
                zIndex: 10
            };
        });
        
        // 选择图片
        const selectImage = () => {
            fileInput.value.click();
        };
        
        // 处理文件选择
        const handleFileSelect = (event) => {
            const file = event.target.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    select_img.value = e.target.result;
                    // 重置图片变换状态
                    imageTransform.value = {
                        width: 67,
                        translateX: 0,
                        translateY: 0
                    };
                    // 触发事件
                    emit('update:modelValue', select_img.value);
                    emit('image-change', select_img.value);
                };
                reader.readAsDataURL(file);
            }
        };
        
        // 计算两点间距离
        const getDistance = (touch1, touch2) => {
            const dx = touch1.clientX - touch2.clientX;
            const dy = touch1.clientY - touch2.clientY;
            return Math.sqrt(dx * dx + dy * dy);
        };
        
        // 触摸开始
        const handleTouchStart = (event) => {
            event.preventDefault();
            touchState.value.isTouch = true;
            touchState.value.touches = Array.from(event.touches);
            
            if (event.touches.length === 1) {
                // 单指拖动
                touchState.value.startX = event.touches[0].clientX;
                touchState.value.startY = event.touches[0].clientY;
                touchState.value.startTranslateX = imageTransform.value.translateX;
                touchState.value.startTranslateY = imageTransform.value.translateY;
            } else if (event.touches.length === 2) {
                // 双指缩放
                touchState.value.startDistance = getDistance(event.touches[0], event.touches[1]);
                touchState.value.startWidth = imageTransform.value.width;
            }
        };
        
        // 触摸移动
        const handleTouchMove = (event) => {
            event.preventDefault();
            
            if (event.touches.length === 1 && touchState.value.touches.length === 1) {
                // 单指拖动
                const deltaX = event.touches[0].clientX - touchState.value.startX;
                const deltaY = event.touches[0].clientY - touchState.value.startY;
                
                imageTransform.value.translateX = touchState.value.startTranslateX + deltaX;
                imageTransform.value.translateY = touchState.value.startTranslateY + deltaY;
            } else if (event.touches.length === 2 && touchState.value.touches.length === 2) {
                // 双指缩放
                const currentDistance = getDistance(event.touches[0], event.touches[1]);
                const scaleRatio = currentDistance / touchState.value.startDistance;
                const newWidth = touchState.value.startWidth * scaleRatio;

                // 限制缩放范围（转换为宽度范围）
                const minWidth = 67 * props.minScale; // 最小宽度
                const maxWidth = 67 * props.maxScale; // 最大宽度
                imageTransform.value.width = Math.max(minWidth, Math.min(maxWidth, newWidth));
            }
        };
        
        // 触摸结束
        const handleTouchEnd = (event) => {
            event.preventDefault();
            touchState.value.isTouch = false;
            touchState.value.touches = Array.from(event.touches);
        };
        
        // 鼠标按下
        const handleMouseDown = (event) => {
            event.preventDefault();
            mouseState.value.isDragging = true;
            mouseState.value.startX = event.clientX;
            mouseState.value.startY = event.clientY;
            mouseState.value.startTranslateX = imageTransform.value.translateX;
            mouseState.value.startTranslateY = imageTransform.value.translateY;
        };
        
        // 鼠标移动
        const handleMouseMove = (event) => {
            if (!mouseState.value.isDragging) return;
            event.preventDefault();
            
            const deltaX = event.clientX - mouseState.value.startX;
            const deltaY = event.clientY - mouseState.value.startY;
            
            imageTransform.value.translateX = mouseState.value.startTranslateX + deltaX;
            imageTransform.value.translateY = mouseState.value.startTranslateY + deltaY;
        };
        
        // 鼠标释放
        const handleMouseUp = (event) => {
            event.preventDefault();
            mouseState.value.isDragging = false;
        };
        
        // 滚轮缩放
        const handleWheel = (event) => {
            event.preventDefault();
            const delta = event.deltaY > 0 ? -6.7 : 6.7; // 每次滚动改变6.7vw（相当于0.1倍缩放）
            const newWidth = imageTransform.value.width + delta;

            // 限制缩放范围
            const minWidth = 67 * props.minScale;
            const maxWidth = 67 * props.maxScale;
            imageTransform.value.width = Math.max(minWidth, Math.min(maxWidth, newWidth));
        };
        
        // 监听props变化
        Vue.watch(() => props.modelValue, (newValue) => {
            select_img.value = newValue;
        });

        // 暴露方法给父组件
        const { expose } = Vue.getCurrentInstance() || {};
        if (expose) {
            expose({
                selectImage
            });
        }

        return {
            fileInput,
            select_img,
            imageStyle,
            addStle,
            selectImage,
            handleFileSelect,
            handleTouchStart,
            handleTouchMove,
            handleTouchEnd,
            handleMouseDown,
            handleMouseMove,
            handleMouseUp,
            handleWheel
        };
    },
    template: `
        <div class="image-selector-container">
            <img v-if="select_img" :src="select_img" class="select_img"
                 :style="imageStyle"
                 @touchstart="handleTouchStart"
                 @touchmove="handleTouchMove"
                 @touchend="handleTouchEnd"
                 @mousedown="handleMouseDown"
                 @mousemove="handleMouseMove"
                 @mouseup="handleMouseUp"
                 @wheel="handleWheel">
            <div v-if="!select_img && showAddButton" class="add" :style="addStle" @click="selectImage"></div>
            <input type="file" ref="fileInput" @change="handleFileSelect" accept="image/*" style="display: none;">
        </div>
    `
};

// 将组件挂载到全局
window.ImageSelectorComponent = ImageSelectorComponent;
